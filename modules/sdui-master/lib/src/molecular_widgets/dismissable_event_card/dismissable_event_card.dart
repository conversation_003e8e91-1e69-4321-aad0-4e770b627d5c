import 'package:design_module/utilities/color_constants.dart';
import 'package:flutter/material.dart';
import 'package:sdui/sdui.dart';
import 'package:utilities/utilities.dart';

class DismissableEventCard extends SDUIStatefulWidget {
  DismissableEventCard({Key? key}) : super(key: key);
  Widget? content, dismissableButtonWidget;
  EdgeInsets? margin;
  bool? isVisibleByDefault;

  @override
  fromJson(Map<String, dynamic>? json) {
    if (json != null) {
      if (json['dismissableButtonWidget'] != null) {
        dismissableButtonWidget =
            SDUIParser.getInstance().fromJson(json['dismissableButtonWidget']);
      }
      if (json['content'] != null) {
        content = SDUIParser.getInstance().fromJson(json['content']);
      }
      if (json['margin'] != null) {
        margin = getEdgeInsets(json['margin']);
      }
      if (json['isVisibleByDefault'] != null) {
        isVisibleByDefault = json['isVisibleByDefault'];
      }
    }
    if (json?["actions"] != null) {
      if (json!["actions"] is Map<String, dynamic>) {
        action.addAll([SDUIAction().fromJson(json["actions"])]);
      } else {
        var children = json["actions"];
        if (children is List<dynamic>) {
          action.addAll(children.map((e) => SDUIAction().fromJson(e)).toList());
        }
      }
    }

    return super.fromJson(json);
  }

  @override
  State<StatefulWidget> createState() => DismissableEventCardState(this);
}

class DismissableEventCardState extends State<DismissableEventCard>
    with AutomaticKeepAliveClientMixin {
  DismissableEventCardState(this.delegate);
  DismissableEventCard delegate;
  final StateProvider _stateProvider = StateProvider();
  bool isVisible = true;
  bool isAnimating = false;

  @override
  void initState() {
    super.initState();
    isVisible = delegate.isVisibleByDefault ?? true;
    for (var analytic in delegate.analytics) {
      analytic.executeAction(context, null);
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!isVisible && delegate.isVisibleByDefault != true) {
      return const SizedBox.shrink();
    }

    return AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
        transform:
            Matrix4.translationValues(isAnimating ? 40.0 : 0.0, 0.0, 0.0),
        child: AnimatedOpacity(
          opacity: isAnimating ? 0.0 : 1.0,
          duration: const Duration(milliseconds: 300),
          child: Container(
            margin: delegate.margin,
            child: Stack(
              children: [
                delegate.content ?? const SizedBox.shrink(),
                if (delegate.dismissableButtonWidget != null)
                  Positioned(
                    top: 0,
                    right: 0,
                    child: MultipleGestureDetector(
                      onTap: () {
                        _stateProvider.notify(ObserverState.PROFILE_REFRESH);
                        _stateProvider.notify(ObserverState.MyAccount_Refresh);
                        if (mounted) {
                          setState(() {
                            isAnimating = true;
                          });
                        }
                        Future.delayed(const Duration(milliseconds: 300), () {
                          if (mounted) {
                            setState(() {
                              isVisible = false;
                            });
                          }
                        });
                      },
                      child: delegate.dismissableButtonWidget!,
                    ),
                  ),
              ],
            ),
          ),
        ));
  }

  WidgetBackground getWidgetBackground(WidgetBackground? background) {
    return WidgetBackground(
      color: background?.color ?? colorFFFFFF,
      gradient: background?.gradient,
      opacity: background?.opacity,
      elevation: background?.elevation,
      activeColor: background?.activeColor,
      inactiveColor: background?.inactiveColor,
    );
  }

  @override
  bool get wantKeepAlive => isVisible;
}
