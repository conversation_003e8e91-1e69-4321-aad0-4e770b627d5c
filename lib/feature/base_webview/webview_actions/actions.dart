class AckoBaseWebViewActions {
  static const String INTENT = 'intent';
  static const String LOG_IN = "login";
  static const String ADD_APP_BAR_ACTIONS = "add_app_bar_actions";
  static const String SEND_EVENT = "send_event";
  static const String SHOW_IN_APP_RATING = "show_in_app_rating";
  static const String FETCH_USER_LOCATION = "fetch_user_location";
  static const String OPEN_SETTINGS_PAGE = "open_settings_page";
  static const String NCB_WEB_PAGE = "ncb_link";
  static const String CHANGE_ORIENTATION = 'change_orientation';
  static const String NATIVE_SHARE = "native_share";
  static const String WEB_PERMISSION = "web_permission";
  static const String ASK_LOCATION_PERMISSION = "ask_location_permission";
  static const String ASK_APP_PERMISSION = "ask_app_permission";
  static const String CLOSE_WEB_VIEW = "close_webview";
  static const String NATIVE_BACK_PRESS = "native_back_press";
  static const String CAN_USE_NATIVE_BACK_PRESS = "can_use_native_back_press";
  static const String OPEN_NATIVE_CAMERA = "open_native_camera";
  static const String GET_USER_ID = "get_user_id";
  static const String INIT_PAYMENT_SDK = "init_payment_sdk";
  static const String OPEN_DEEPLINK = "open_deeplink";
  static const String USER_LOGIN_META_DATA = 'user_login_meta_data';
  static const String INITIATE_MOBILE_AUTO_READ = 'initiate_mobile_auto_read';
  static const String INITIATE_OTP_AUTO_READ = 'initiate_otp_auto_read';
  static const String PLAY_VIDEO = 'play_video';
   static const String OPEN_BROWSER_TAB = "open_browser_tab";
  static const String CLOSE_BROWSER_TAB = "close_browser_tab";
}
