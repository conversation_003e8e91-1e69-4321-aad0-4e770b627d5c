import 'dart:convert';
import 'dart:io';

import 'package:acko_core_utilities/deeplink_handler/DeeplinkHandler.dart';
import 'package:acko_flutter/feature/webview/web_view_model.dart';
import 'package:acko_flutter/util/extensions.dart';
import 'package:acko_flutter/util/file_picker/file_picker.dart';
import 'package:acko_flutter/util/screeen_utils/screen_utils.dart';
import 'package:acko_logger/acko_logger.dart';
import 'package:acko_logger/events/info/webview_info_event.dart';
import 'package:acko_web_view_module/bloc/states.dart';
import 'package:acko_web_view_module/common/custom_dartz.dart';
import 'package:acko_web_view_module/lob_contract_classes/view_contract.dart';
import 'package:acko_web_view_module/view/acko_web_view_module.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:session_manager_module/session_manager.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:utilities/constants/constants.dart';

import '../../../common/util/EventsHelper.dart';
import '../../../common/util/strings.dart';
import '../../../r2d2/events.dart';
import '../../../util/Utility.dart';
import '../../../util/download_util.dart';
import '../../../util/health/utils.dart';
import '../../payment/usecase/payment_usecase.dart';
import '../webview_actions/actions.dart';

class BaseWebViewLogic extends WebViewUIBase with PaymentUseCases {
  late InAppWebViewController webviewController;
  bool initiateAutoMobileReadCalled = false;
  bool initiateAutoOtpReadCalled = false;
  ChromeSafariBrowser? crometab;
  @override
  void addJsHandler(InAppWebViewController controller) {
    controller.addJavaScriptHandler(
      handlerName: 'blobToBase64Handler',
      callback: (data) async {
        if (data.isNotEmpty) {
          final String receivedFileInBase64 = data[0];
          Downloader.createFileFromBase64(receivedFileInBase64,
              'acko-download-${DateTime.now().millisecond}', "pdf");
        }
      },
    );
  }

  @override
  void blocStateListener(WebViewState state) {}

  @override
  Future<Either<NotHandlingMethod, bool>> canGoBack(
      InAppWebViewController controller) {
    return Future.value(Left(NotHandlingMethod()));
  }

  @override
  Future<Either<NotHandlingMethod, dynamic>> handleWebViewActions(
      args, InAppWebViewController controller) async {
    var result;

    Map<String, dynamic> jsonData = jsonDecode(args.trim());
    WebViewModel model = WebViewModel.fromJson(jsonData);
    switch (model.action) {
      case AckoBaseWebViewActions.CHANGE_ORIENTATION:
        (model.actionValue?.orientation ?? "vertical") == "horizontal"
            ? ScreenUtils.setLandscapeOrientation()
            : ScreenUtils.setPortraitOrientation();
        break;
      case AckoBaseWebViewActions.USER_LOGIN_META_DATA:
        result = model.actionValue?.phoneNumber ?? "0";
        if ((model.actionValue?.phoneNumber ?? "").isNotNullAndEmpty) {
          await updateUserPhoneNumber(model.actionValue!.phoneNumber!);
        }
        break;
      case AckoBaseWebViewActions.INITIATE_MOBILE_AUTO_READ:
        initiateAutoMobileReadCalled = true;
        Constants.PLATFORM_CHANNEL.invokeMapMethod("show_phone_number_hint");
        Constants.LOCAL_TO_SCREEN_PLATFORM_CHANNEL
            .setMethodCallHandler(_setMethodCallHandler);
        break;
      case AckoBaseWebViewActions.INITIATE_OTP_AUTO_READ:
        initiateAutoOtpReadCalled = true;
        Constants.PLATFORM_CHANNEL.invokeMapMethod("start_sms_listener");
        Constants.LOCAL_TO_SCREEN_PLATFORM_CHANNEL
            .setMethodCallHandler(_setMethodCallHandler);
        break;
      case AckoBaseWebViewActions.OPEN_DEEPLINK:
        if (pageContext != null &&
            model.actionValue != null &&
            model.actionValue!.deeplink.isNotNullAndEmpty) {
          var map = model.actionValue!.deeplink!.getRoute();
          if (map?['route'] != null) {
            Navigator.pushNamed(pageContext!, map?["route"], arguments: map);
          }
        }
        break;
      case AckoBaseWebViewActions.CLOSE_WEB_VIEW:
        Navigator.pop(pageContext!);
        break;
      case AckoBaseWebViewActions.INIT_PAYMENT_SDK:
        await initJuspaySDK();
        return Right(true);
      case AckoBaseWebViewActions.GET_USER_ID:
        return await _getUserCookie();
      case AckoBaseWebViewActions.CAN_USE_NATIVE_BACK_PRESS:
        return Right(true);
      case AckoBaseWebViewActions.NATIVE_BACK_PRESS:
        Navigator.maybePop(pageContext!);
        break;
      case AckoBaseWebViewActions.OPEN_NATIVE_CAMERA:
        final result = await _handleOpenNativeCameraAction();
        return Right(result);
      case AckoBaseWebViewActions.INTENT:
        launchIntent(model);
        return Right(null);
      case AckoBaseWebViewActions.LOG_IN:
        await logoutHandling();
        Navigator.pushNamedAndRemoveUntil(
            pageContext!, Routes.LOGIN_PAGE, (route) => false,
            arguments: {
              "is_user_logged_out": true,
              "phone_number": model.actionValue?.phoneNumber,
              "next": model.actionValue?.next
            });
        break;
      case AckoBaseWebViewActions.ADD_APP_BAR_ACTIONS:
        if (model.actionValue?.appBarActions != null) {
          pageContext!
              .findAncestorStateOfType<AckoWebViewState>()
              ?.addAppBarActions(model.actionValue!.appBarActions!);
        }
        break;
      case AckoBaseWebViewActions.SEND_EVENT:
        sendEvents(model);
        break;
      case AckoBaseWebViewActions.NATIVE_SHARE:
        if (model.actionValue?.text?.isNotNullAndEmpty ?? false)
          Constants.PLATFORM_CHANNEL.invokeMethod(Constants.shareIntent,
              <String, dynamic>{Constants.shareText: model.actionValue?.text});
        break;
      case AckoBaseWebViewActions.WEB_PERMISSION:
        PermissionStatus permissionStatus = await Permission.camera.request();
        return Right(
            permissionStatus.toString().replaceAll('PermissionStatus.', ''));
      case AckoBaseWebViewActions.SHOW_IN_APP_RATING:
        Util.showInAppReview();
        break;
      case AckoBaseWebViewActions.FETCH_USER_LOCATION:
        var map = await getLocation();
        return Future.value(Right(jsonEncode(map)));
      case AckoBaseWebViewActions.ASK_LOCATION_PERMISSION:
        Util.askForAppPermission(Permission.location);
        break;
      case AckoBaseWebViewActions.ASK_APP_PERMISSION:
        if ((model.actionValue?.permissionRequest ?? "").isNotNullAndEmpty) {
          Permission? permission;
          switch (model.actionValue!.permissionRequest) {
            case 'camera':
              permission = Permission.camera;
              break;
            case 'location':
              permission = Permission.location;
              break;
            case 'gallery':
              permission = Permission.photos;
              break;
          }
          if (permission != null) {
            Util.askForAppPermission(permission);
          }
        }
        break;
      case AckoBaseWebViewActions.OPEN_SETTINGS_PAGE:
        _handleOpenSettingsPageAction();
        break;
      case AckoBaseWebViewActions.PLAY_VIDEO:
        UiUtils.getInstance.showVideoPopup(
            pageContext!, jsonData['action_value'],
            orientation: jsonData['orientation']);
        break;
      case AckoBaseWebViewActions.OPEN_BROWSER_TAB:
        crometab = ChromeSafariBrowser();
        await crometab?.open(url: WebUri(model.actionValue?.redirectUrl ?? ''));
        break;
      case AckoBaseWebViewActions.CLOSE_BROWSER_TAB:
        await crometab?.close();
        break;
      default:
        return Future.value(Left(NotHandlingMethod()));
    }

    return Future.value(Right(result));
  }

  Future<void> _setMethodCallHandler(MethodCall call) async {
    switch (call.method) {
      case "phone_number_received":
        String? phoneNumber = call.arguments;
        R2D2Events.instance.trackMobileNumberEntry(phoneNumber);
        await webviewController.evaluateJavascript(
            source: "setPhoneNumber('$phoneNumber');");
        break;
      case "otp_received":
        String? otp = call.arguments;
        await webviewController.evaluateJavascript(source: "setOtp('$otp');");
        break;
    }
    return Future.value();
  }

  updateUserPhoneNumber(String phone) async {
    await SessionManager.instance.storageManager
        .updateSessionDetails(UserSessionModel(phone: phone));
  }

  _getUserCookie() async {
    final sessionDetails =
        await SessionManager.instance.storageManager.getUserSessionDetails();

    return sessionDetails.accessToken;
  }

  void _handleOpenSettingsPageAction() {
    if (Platform.isIOS)
      openAppSettings();
    else
      Constants.PLATFORM_CHANNEL
          .invokeMethod("settings", {"type": "permission"});
  }

  Future<Map?> getLocation() async {
    var serviceStatus = await Permission.location.status;
    String permissionStatus = _getPermissionStatus(serviceStatus);
    if (!permissionStatus.equalIgnoreCase("success")) {
      serviceStatus = await Permission.location.request();
      permissionStatus = _getPermissionStatus(serviceStatus);
    }
    if (serviceStatus == PermissionStatus.granted) {
      final result = await Constants.PLATFORM_CHANNEL
          .invokeMethod("splash_location_event");
      if (result is Map) {
        return Future.value({
          "lat": result["latitude"],
          "long": result["longitude"],
          "accuracy": result["accuracy"],
          "status": result["permission"] == true ? "success" : permissionStatus
        });
      }
    }
    return Future.value({"status": permissionStatus});
  }

  String _getPermissionStatus(PermissionStatus serviceStatus) {
    if (serviceStatus case PermissionStatus.granted) {
      return "success";
    } else if (serviceStatus case PermissionStatus.provisional) {
      return "success";
    } else
      return "permission_denied";
  }

  Future<void> sendEvents(WebViewModel model) async {
    if (model.actionValue != null) {
      if (model.actionValue!.destination!.contains("firebase")) {
        Map<String, dynamic> params = new Map();
        if (model.actionValue!.eventParams != null) {
          model.actionValue!.eventParams!.forEach((element) {
            params = element;
          });
        }
        EventsHelper.logFirebaseEvent(model.actionValue!.eventName!, params);
      }
    }
  }

  launchIntent(WebViewModel model) async {
    if (model.actionValue?.url != null &&
        await canLaunchUrl(Uri.parse(model.actionValue?.url ?? ''))) {
      final uri = Uri.parse(model.actionValue?.url ?? '');
      await launchUrl(
        uri,
        mode: Platform.isIOS
            ? LaunchMode.externalApplication // open in Safari on iOS
            : LaunchMode.platformDefault,
      );
    } else {
      Fluttertoast.showToast(msg: somethingWentWrong);
    }
  }

  Future<dynamic> _handleOpenNativeCameraAction() async {
    final result = await _onCameraTapped(pageContext);
    if (result != null) {
      final bytes = File((result as FileInfo).path).readAsBytesSync();
      String img64 = base64Encode(bytes);
      return {
        "data": img64,
        "file_name": result.name,
        "size": result.size,
      };
    }
  }

  _onCameraTapped(context) async {
    if (Platform.isIOS) {
      bool _isPermissionDenied = await _isIOSCameraPermissionDenied();
      if (_isPermissionDenied) {
        UiUtils.getInstance
            .showToast(enable_permissions, toastLength: Toast.LENGTH_LONG);
        return;
      }
    }
    try {
      FileInfo? _result = await FilePicker.getInstance
          .captureCompressedImage(selfieCamera: false);
      return _result;
    } catch (error) {
      if (error is PlatformException && error.code == 'permission') {
        UiUtils.getInstance
            .showToast(enable_permissions, toastLength: Toast.LENGTH_LONG);
      }
      return;
    }
  }

  static Future<bool> _isIOSCameraPermissionDenied() async {
    PermissionStatus cameraPermissionStatus = await Permission.camera.status;
    return cameraPermissionStatus == PermissionStatus.permanentlyDenied;
  }

  @override
  void onCloseWindow(InAppWebViewController controller) {
    // TODO: implement onCloseWindow
  }

  @override
  void onConsoleMessage(
      InAppWebViewController controller, ConsoleMessage message) {
    // TODO: implement onConsoleMessage
  }

  @override
  Either<NotHandlingMethod, HandlingVoidMethod> onCreateWindow(
      InAppWebViewController controller, CreateWindowAction request) {
    // TODO: implement onCreateWindow
    return Left(NotHandlingMethod());
  }

  @override
  Either<NotHandlingMethod, HandlingVoidMethod> onDownloadStart(
      InAppWebViewController controller,
      DownloadStartRequest downloadStartRequest) {
    // TODO: implement onDownloadStart
    return Left(NotHandlingMethod());
  }

  @override
  void onLoadError(
      InAppWebViewController controller, Uri? url, int code, String message) {
    if (!url.toString().contains('/loginsuccess')) {
      AckoLoggerManager.instance.logInfo(
          event: WebviewInfoEvent(
              webviewCurrentState: "webview_load_error",
              url: url.toString(),
              page: 'webview',
              data: {"error": message, "code": code}));
    }
  }

  @override
  void onLoadStart(InAppWebViewController controller, Uri? url) {
    // TODO: implement onLoadStart
  }

  @override
  void onLoadStop(InAppWebViewController controller, Uri? url) {}

  @override
  void onPageCommitVisible(InAppWebViewController controller, Uri? url) {
    // TODO: implement onPageCommitVisible
  }

  @override
  void onWebViewCreated(InAppWebViewController controller) {
    // TODO: implement onWebViewCreated
  }

  @override
  Future<Either<NotHandlingMethod, NavigationActionPolicy>>
      shouldOverrideUrlLoading(
          InAppWebViewController controller, NavigationAction action) async {
    String url = action.request.url.toString();
    if (url.contains('v1/signin') ||
        url.contains('platform/auth/token/acko_app')) {
      return Future.value(Right(NavigationActionPolicy.ALLOW));
    } else if (url.contains('loginsuccess')) {
      final model = await extractUserAuthData();
      Navigator.pop(pageContext!, model);
      return Future.value(Right(NavigationActionPolicy.CANCEL));
    } else if ((action.request.url?.scheme ?? "").equalIgnoreCase('mailto') &&
        (await canLaunchUrl(action.request.url!))) {
      await launchUrl(action.request.url!);
      return Future.value(Right(NavigationActionPolicy.CANCEL));
    }
    return Future.value(Left(NotHandlingMethod()));
  }

  @override
  Future<bool> shouldSubtractKeypadSpacing(String url) {
    // TODO: implement shouldSubtractKeypadSpacing
    return Future.value(false);
  }

  @override
  void dispose() {
    // TODO: implement dispose
  }

  @override
  Either<NotHandlingMethod, HandlingVoidMethod> onAppBarActionPressed(
      String actionValue) {
    return Left(NotHandlingMethod());
  }

  Future<UserSessionModel?> extractUserAuthData() async {
    final cookies = await CookieManager.instance()
        .getCookies(url: WebUri(Constants.BASE_URL));
    String? userEkey;
    String? accessToken;
    String? refreshToken;
    String? phoneNumber =
        (await SessionManager.instance.storageManager.getUserSessionDetails())
            .phone;
    if (cookies.isNotEmpty) {
      accessToken = cookies
          .firstWhere((element) => element.name.contains('user_id'))
          .value;
      refreshToken = cookies
          .firstWhere((element) => element.name.contains('refresh_token'))
          .value;
      userEkey = cookies
          .firstWhere((element) => element.name.contains('auth_uid'))
          .value;
      UserSessionModel model = UserSessionModel(
          userEkey: userEkey!,
          accessToken: accessToken!,
          refreshToken: refreshToken!,
          phone: phoneNumber);
      await SessionManager.instance.storageManager.updateSessionDetails(model);
      return model;
    }
    return null;
  }

  @override
  void onPageCompleteLoaded(InAppWebViewController controller) {}
}
