import 'package:acko_flutter/feature/staggered_profile_building/Parser/question_parser.dart';
import 'package:acko_flutter/feature/staggered_profile_building/Repository/question_page_repo.dart';
import 'package:acko_flutter/feature/staggered_profile_building/Utils/question_view_constants.dart';
import 'package:acko_flutter/feature/staggered_profile_building/bloc/question_states.dart';
import 'package:acko_flutter/util/extensions.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter/material.dart';

class QuestionPageCubit extends Cubit<QuestionStates> {
  final QuestionPageRepo repository;
  final QuestionPageParser parser;
  String? questionId;
  bool dismissLoader = false;

  QuestionPageCubit(this.repository, this.parser) : super(QuestionPageInitialLoadingState());

  void fetchQuestionPage(String currentNodeId) async {
    final result = await repository.getQuestionPageUIConfig(currentNodeId);
    parseResponseAndLoadUI(result);
  }

  void submitAnswer(String currentNodeId, String answer) async {
    emit(QuestionPageSubmitLoadingState());
    final result = await repository.submitAnswerAndGetNextPage(currentNodeId, questionId, answer);
    dismissLoader = true;
    parseResponseAndLoadUI(result);
  }

  void parseResponseAndLoadUI(Map<String, dynamic> result) {
    parser.uiWidget = null;
    parser.finalPage = null;

    if (result.containsKey(QuestionViewConstants.error)) {
      emit(QuestionPageErrorState());
      return;
    }
    final questions = result[QuestionViewConstants.data][QuestionViewConstants.questions] as List<dynamic>?;
    if (questions.isNotNullOrEmpty) {
      questionId = questions!.first[QuestionViewConstants.questionId];
    }

    try {
      final uiConfig = result[QuestionViewConstants.uiConfig];
      final widget = parser.fromJson(uiConfig);

      emit(QuestionPageLoadedState(widget: parser.uiWidget, finalPage: parser.finalPage, pageBackground: parser.pageBackground));
    } catch (e) {
      emit(QuestionPageLoadedState(widget: null));
    }
  }

  void executeAction(BuildContext context, QuestionEventType type, Map<String, dynamic>? data) {
    if (context.mounted) {
      switch (type) {
        case QuestionEventType.LOADER:
          parser.loaderEventAction?.executeAction(context, data);
          parser.loaderEventAction = null;
          break;
        case QuestionEventType.SUBMIT_ANSWER:
          parser.tapEventAction?.executeAction(context, data);
          parser.tapEventAction = null;
          break;
        case QuestionEventType.VIEW_EVENT:
          parser.analyticsAction?.executeAction(context, data);
          parser.analyticsAction = null;
          break;
        case QuestionEventType.BACK_ACTION:
          parser.backEventAction?.executeAction(context, data);
          parser.backEventAction = null;
          break;
      }
    }
  }
}
