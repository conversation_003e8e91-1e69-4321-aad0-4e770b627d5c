import 'package:acko_flutter/common/util/color_constants.dart';
import 'package:acko_flutter/common/util/strings.dart';
import 'package:acko_flutter/feature/acko_services/ui/acko_services_initial_screen.dart';
import 'package:acko_flutter/feature/staggered_profile_building/Utils/custom_bottom_animation.dart';
import 'package:acko_flutter/feature/staggered_profile_building/Utils/question_view_constants.dart';
import 'package:acko_flutter/feature/staggered_profile_building/View/question_full_page_shimmer.dart';
import 'package:acko_flutter/feature/staggered_profile_building/bloc/question_cubit.dart';
import 'package:acko_flutter/feature/staggered_profile_building/bloc/question_states.dart';
import 'package:acko_flutter/util/Utility.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:lottie/lottie.dart';
import 'package:sdui/sdui.dart';
import 'package:utilities/state_provider/StateProvider.dart';

class QuestionPageView extends StatefulWidget {
  final String currentNodeId;
  final String? fromPage;

  const QuestionPageView({super.key, required this.currentNodeId, this.fromPage = 'home'});

  @override
  State<QuestionPageView> createState() => _QuestionPageViewState();
}

class _QuestionPageViewState extends State<QuestionPageView> {
  late final QuestionPageCubit _cubit;
  int? selectedYear;
  StateProvider _stateProvider = StateProvider();

  @override
  void initState() {
    super.initState();
    _cubit = BlocProvider.of<QuestionPageCubit>(context);
    _cubit.fetchQuestionPage(widget.currentNodeId);
  }

  @override
  void dispose() {
    _cubit.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<QuestionPageCubit, QuestionStates>(
      listener: (context, state) {
    
        final data = {
          QuestionViewConstants.fromPage: widget.fromPage,
        };
        if (_cubit.dismissLoader) {
          Navigator.of(context, rootNavigator: true).pop();
          _cubit.dismissLoader = false;
        }
        if (state is QuestionPageSubmitLoadingState) {
          _cubit.executeAction(context, QuestionEventType.LOADER, data);
          showQuestionSubmitLoader(context);
        }
        if (state is QuestionPageLoadedState) {
          _cubit.executeAction(context, QuestionEventType.VIEW_EVENT, data);
        }
      },
      builder: (context, state) {
    
        Widget? pageContent;
        WidgetBackground? background;
    
        if (state is QuestionPageLoadedState) {
          background = state.pageBackground;
    
          if (state.finalPage != null) {
            pageContent = state.finalPage;
          } else if (state.widget != null) {
            state.widget!.optionSelector?.onCallbackNeeded = (id, value) {
               String attributeId = id;
               final data = {
                QuestionViewConstants.fromPage: widget.fromPage,
                QuestionViewConstants.selected_value: value
              };
              _cubit.executeAction(context, QuestionEventType.SUBMIT_ANSWER, data);
               if(selectedYear != null) {
                 attributeId = '$attributeId $selectedYear';
                 selectedYear = null;
               }
              _cubit.submitAnswer(QuestionViewConstants.submitAnswerNeedState, id);
            };
            state.widget!.stepperView?.onYearChanged = (year) {
              selectedYear = year;
            };
            pageContent = Center(child: state.widget);
          }
    
          return Stack(
            children: [
              Container(
                decoration: BoxDecoration(
                  gradient: background?.gradient,
                  color: background?.color,
                ),
                child: AnimatedSwitcher(
                  duration: const Duration(milliseconds: 300),
                  switchInCurve: Curves.linear,
                  switchOutCurve: Curves.easeOutCubic,
                  transitionBuilder: (Widget child, Animation<double> animation) {
    
                    return FadeTransition(
                      opacity: animation,
                      child: child,
                    );
                  },
                  child: KeyedSubtree(
                    key: ValueKey(state.widget.hashCode),
                    child: BottomFadeInWidget(
                      duration: const Duration(milliseconds: 400),
                      child: pageContent!,
                    ),
                  ),
                ),
              ),
              Positioned(
                top: 60,
                left: 16,
                child: IconButton(
                  icon: const Icon(Icons.arrow_back_ios_new_rounded),
                  color: colorFFFFFF,
                  iconSize: 24,
                  onPressed: () {
                    _cubit.executeAction(context, QuestionEventType.BACK_ACTION, null);
                    Navigator.of(context).pop();
                  },
                ),
              ),
            ],
          );
    
    
        } else if (state is QuestionPageInitialLoadingState) {
          return FullScreenDiagonalShimmer();
        } else if (state is QuestionPageErrorState) {
          return AckoServicesIntiailScreen(
            isOutlinedButton: true,
            title: something_went_wrong,
            imgUrl: Util.getAssetImage(assetName: QuestionViewConstants.bucketDrop),
            btnTitle: try_again,
            onTap: () {
              _cubit.fetchQuestionPage(widget.currentNodeId);
            },
          );
        }
        return const SizedBox.shrink();
      },
      buildWhen: (context, state) {
        return state is QuestionPageLoadedState || state is QuestionPageInitialLoadingState || state is QuestionPageErrorState;
      },
    );

  }


  void showQuestionSubmitLoader(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      barrierColor: Colors.black.withOpacity(0.6),
        builder: (context) {
          return BottomFadeInWidget(child: Center(
            child: Material(
              color: Colors.transparent,
              child: Container(
                width: 212,
                margin: const EdgeInsets.symmetric(horizontal: 32),
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(24),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const SizedBox(height: 32),
                    SizedBox(
                      height: 96,
                      width: 96,
                      child: Lottie.asset(
                        QuestionViewConstants.loaderAnimationAsset,
                        repeat: true,
                      ),
                    ),
                    const SizedBox(height: 16),
                    SDUIText(
                      value:  QuestionViewConstants.loaderText,
                      textStyle: QuestionViewConstants.loaderTextStyle,
                      textColor: color000000,
                      alignment: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
          ));
        }
    );
  }
}


