import 'package:acko_flutter/common/util/color_constants.dart';
import 'package:acko_flutter/feature/staggered_profile_building/Utils/question_view_constants.dart';
import 'package:acko_flutter/util/extensions.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:sdui/sdui.dart';
import 'package:utilities/state_provider/StateProvider.dart';
import 'package:utilities/utilities.dart';

class QuestionFinalPageView extends SDUIStatelessWidget {
  Widget? container;
  String? primaryCTAText;
  String? secondaryCTAText;
  List<SDUIAction?> primaryCTAAction = [];
  List<SDUIAction?> secondaryCTAAction = [];
  StateProvider _stateProvider = StateProvider();
  AnalyticsAction? primaryCTAAnalyticsAction;
  AnalyticsAction? secondaryCTAAnalyticsAction;

  static const _horizontalPadding = EdgeInsets.symmetric(horizontal: 20);
  static const _verticalButtonPadding = EdgeInsets.symmetric(vertical: 16, horizontal: 16);
  static const _buttonBorderRadius = BorderRadius.all(Radius.circular(8));

  @override
  fromJson(Map<String, dynamic>? json) {
    if (json != null && json[QuestionViewConstants.uiWidget] != null) {
      final uiWidget = json[QuestionViewConstants.uiWidget];
      container = uiWidget[QuestionViewConstants.container] != null
          ? SDUIParser.getInstance().fromJson(uiWidget[QuestionViewConstants.container])
          : null;

      primaryCTAText = uiWidget[QuestionViewConstants.primaryCTAText] as String?;
      secondaryCTAText = uiWidget[QuestionViewConstants.secondaryCTAText] as String?;

      if(uiWidget[QuestionViewConstants.primaryCtaTapEventAction] != null) {
        final analytics = uiWidget[QuestionViewConstants.primaryCtaTapEventAction][0];
        primaryCTAAnalyticsAction = AnalyticsAction.fromJson(analytics);
      }

      if(uiWidget[QuestionViewConstants.secondaryCtaTapEventAction] != null) {
        final analytics = uiWidget[QuestionViewConstants.secondaryCtaTapEventAction][0];
        secondaryCTAAnalyticsAction = AnalyticsAction.fromJson(analytics);
      }

      final actions = uiWidget[QuestionViewConstants.actions];
      if (actions != null) {
        final primaryActionChildren = actions[QuestionViewConstants.onPrimaryAction];
        if (primaryActionChildren is List) {
          primaryCTAAction = primaryActionChildren.map((e) => SDUIAction().fromJson(e)).toList();
        }

        final secondaryActionChildren = actions[QuestionViewConstants.onSecondaryAction];
        if (secondaryActionChildren is List) {
          secondaryCTAAction = secondaryActionChildren.map((e) => SDUIAction().fromJson(e)).toList();
        }
      }
    }
    return super.fromJson(json);
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: true,
      onPopInvokedWithResult: (didPop, result) {
        if (didPop) {
        _stateProvider.notify(ObserverState.PROFILE_REFRESH);
        _stateProvider.notify(ObserverState.MyAccount_Refresh);
        _stateProvider.notify(ObserverState.HOME_TAB_SCROLL_TO_TOP, data: {"index": 0});
        _stateProvider.notify(ObserverState.REFRESH_PROFILE_BUILDING_ASYNC_WIDGET);
        }
      },
      child: SafeArea(
        bottom: true,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Flexible(
              child: LayoutBuilder(
                builder: (context, constraints) {
                  return Center(
                    child: ConstrainedBox(
                      constraints: BoxConstraints(
                        maxHeight: constraints.maxHeight,
                      ),
                      child: container!,
                    ),
                  );
                },
              ),
            ),
      
            if (primaryCTAText.isNotNullOrEmpty)
              InkWell(
                onTap: () {
                  primaryCTAAnalyticsAction?.executeAction(context, null);
                  _handleCTAAction(context, primaryCTAAction);
                },
                borderRadius: _buttonBorderRadius,
                child: Container(
                  margin: _horizontalPadding,
                  padding: _verticalButtonPadding,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    border: Border.all(color: colorFFFFFF),
                    borderRadius: _buttonBorderRadius,
                  ),
                  child: Center(
                    child: SDUIText(
                      value: primaryCTAText!,
                      textColor: color040222,
                      textStyle: QuestionViewConstants.buttonTextStyle,
                      alignment: TextAlign.center,
                    ),
                  ),
                ),
              ),
      
            if (secondaryCTAText.isNotNullOrEmpty) ...[
              const SizedBox(height: 12),
              InkWell(
                onTap: () {
                  secondaryCTAAnalyticsAction?.executeAction(context, null);
                 _handleCTAAction(context, secondaryCTAAction);
                },
                child: Center(
                  child: SDUIText(
                    padding: _verticalButtonPadding,
                    value: secondaryCTAText!,
                    textColor: colorFFFFFF,
                    textStyle: QuestionViewConstants.buttonTextStyle,
                    alignment: TextAlign.center,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  void _handleCTAAction(BuildContext context, List<SDUIAction?> actions) {
    if(context.mounted) {
      for (final element in actions) {
        element?.executeAction(context, null);
      }
    }
  }

}
