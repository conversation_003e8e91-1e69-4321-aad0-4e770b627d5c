import 'package:acko_flutter/util/Utility.dart';
import 'package:acko_flutter/common/widgets/shimmer.dart';
import 'package:acko_flutter/common/util/color_constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sdui/sdui.dart';
import 'package:utilities/state_provider/StateProvider.dart';


class ProfileBuildingAsyncWidget extends StatefulWidget  {
  final String url;
  const ProfileBuildingAsyncWidget({required this.url, super.key});

  @override
  State<ProfileBuildingAsyncWidget> createState() =>
      _ProfileBuildingAsyncWidgetState();
}

class _ProfileBuildingAsyncWidgetState
    extends State<ProfileBuildingAsyncWidget> with StateListener {
  AsyncWidgetCubit? cubit;
  final StateProvider _stateProvider = StateProvider();

  @override
  void initState() {
    super.initState();
    _stateProvider.subscribe(this);
    cubit = AsyncWidgetCubit();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider<AsyncWidgetCubit>(
      create: (context) => cubit!,
      child: AsyncWidget(
        url: widget.url,
        id: "dismissableEventCard",
        widgetJson: Util.getAsyncWidgetJson(widget.url, "dismissableEventCard"),
        shimmerWidget: Padding(
          padding: const EdgeInsets.only(top: 32.0),
          child: Container(
            width: MediaQuery.of(context).size.width,
            height: 120,
            decoration: BoxDecoration(
              color: colorFFFFFF,
              borderRadius: BorderRadius.circular(12),
            ),
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                Shimmer.fromColors(
                  baseColor: colorF3F3F3,
                  highlightColor: colorE7E7F0,
                  child: Container(
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      color: colorF3F3F3,
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Shimmer.fromColors(
                    baseColor: colorF3F3F3,
                    highlightColor: colorE7E7F0,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Container(
                          width: double.infinity,
                          height: 21,
                          decoration: BoxDecoration(
                            color: colorF3F3F3,
                            borderRadius: BorderRadius.circular(4),
                          ),
                        ),
                        const SizedBox(height: 12),
                        Container(
                          width: MediaQuery.of(context).size.width * 0.6,
                          height: 21,
                          decoration: BoxDecoration(
                            color: colorF3F3F3,
                            borderRadius: BorderRadius.circular(4),
                          ),
                        ),
                        const SizedBox(height: 16),
                        Container(
                          width: MediaQuery.of(context).size.width * 0.3,
                          height: 12,
                          decoration: BoxDecoration(
                            color: colorF3F3F3,
                            borderRadius: BorderRadius.circular(4),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
  
  @override
  void onStateChanged(ObserverState state, {data}) {
    if (state == ObserverState.REFRESH_PROFILE_BUILDING_ASYNC_WIDGET) {
      cubit!.refresh(
          widget.url,
          Util.getAsyncWidgetJson(widget.url, "dismissableEventCard"),
          "dismissableEventCard");
    }
  }
}