import 'dart:async';

import 'package:acko_flutter/common/model/drawer_item.dart';
import 'package:acko_flutter/common/util/PreferenceHelper.dart';
import 'package:acko_flutter/common/util/strings.dart';
import 'package:acko_flutter/feature/health_policy/common/health_policy_util.dart';
import 'package:acko_flutter/feature/profile/bloc/UserPorfileRepository.dart';
import 'package:acko_flutter/util/Utility.dart';
import 'package:acko_flutter/util/extensions.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:utilities/widgets/acko_safe_cubit.dart';
import 'package:session_manager_module/session_manager.dart';
import 'package:utilities/state_provider/StateProvider.dart';
import 'package:acko_flutter/common/util/PreferenceHelper.dart' as prefs;

import '../../profile_completion/bloc/bloc_singleton_instance.dart';

class UserProfileBloc extends AckoSafeCubit<UserProfileState>
    implements StateListener {
  late UserProfileRepository _profileRepository;
  EditDetails? userDetails;
  String? lastVisitTime;
  bool? _isAppV10;
  bool? _isStaggeredProfileBuildingEnabled;
  String? _gmcLinkUrl;
  final StateProvider _stateProvider = StateProvider();

  UserProfileBloc() : super(UserProfileState()) {
    StateProvider().subscribe(this);
    _profileRepository = UserProfileRepository();
    _getUserProfile();
    _getLastVisitTime();
  }

  bool get isAppV10 => _isAppV10 ?? false;
  bool get isStaggeredProfileBuildingEnabled =>
      _isStaggeredProfileBuildingEnabled ?? false;
  String? get gmcLinkUrl => _gmcLinkUrl;

  getInitials(String userName) {
    if (userName.isNotNullOrEmpty) {
      List<String> wordsOfName = userName.trim().split(' ').toList();
      return (wordsOfName.length > 1)
          ? '${wordsOfName[0][0].toUpperCase() + wordsOfName[wordsOfName.length - 1][0].toUpperCase()}'
          : '${wordsOfName[0][0].toUpperCase()}';
    } else {
      return '';
    }
  }

  _getUserProfile() async {
    EditDetails? response = ProfileCompeltionBlocSingletonInstance
        .instance.blocInstance
        .getUserProfileData();
    _gmcLinkUrl = await HealthPolicyUtils.getGmcLinkUrl();
    _isAppV10 = await _profileRepository.isAppV10();
    _isStaggeredProfileBuildingEnabled =
        await _profileRepository.isStaggeredProfileBuildingEnabled();
    _updateProfilePageViewed();
    try {
      userDetails = EditDetails(
          phoneNumber: response?.phoneNumber,
          name: response?.name,
          whatsappOptIn: response?.whatsappOptIn,
          email: response?.email,
          dateOfBirth: response?.dateOfBirth,
          gender: response?.gender);
      setStringPrefs(
          StringDataSharedPreferenceKeys.USER_PROFILE_NAME, response?.name);
      setStringPrefs(StringDataSharedPreferenceKeys.EMAIL_ID, response?.email);
      if (!this.isClosed) emit(UserProfileState());
    } catch (error, stacktrace) {
      FirebaseCrashlytics.instance.recordError(
          error, stacktrace); // Firebase crashlytics recording an error.
      Zone.current.handleUncaughtError(error, stacktrace);
      if (!this.isClosed) emit(ErrorState(something_went_wrong));
    }
  }

  _updateProfilePageViewed() async {
    if (isAppV10 && isStaggeredProfileBuildingEnabled) {
      await prefs.setViewedProfilePage(true);
      _stateProvider.notify(ObserverState.VIEWED_PROFILE_PAGE);
    }
  }

  _getLastVisitTime() async {
    var response = await _profileRepository.getLastVisitDetails();
    if (response.error == null && response.lastVisitDate != null) {
      lastVisitTime = response.lastVisitDate;
    }
    if (!this.isClosed) emit(UserProfileState());
  }

  getLogout() async {
    await logoutHandling();
    emit(UserLogoutSate());
  }

  @override
  void onStateChanged(ObserverState state, {data}) {
    if (state == ObserverState.PROFILE_REFRESH) {
      _getUserProfile();
    }
  }
}

class ErrorState extends UserProfileState {
  final String errorResponse;

  ErrorState(this.errorResponse);
}

class UserProfileState {}

class UserLogoutSate extends UserProfileState {}
