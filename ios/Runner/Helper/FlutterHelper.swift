//
//  FlutterHelper.swift
//  Runner
//
//  Created by <PERSON><PERSON> on 01/12/22.
//

import Flutter
import UIKit
import StoreKit
import AppsFlyerLib
import EventKit
import WebEngage
import CoreMedia
import HyperSDK
import Contacts
import ContactsUI
import AppTrackingTransparency
import AdSupport
import GoogleMaps


class FlutterHelper {
  
  public var flutterEngine: FlutterEngine
  public var flutterViewController: FlutterViewController
  
  private var versionNumber = Bundle.AppVersion
  private var buildNumber = Int(Bundle.BuildNumber) ?? 1
  private var channel = "com.acko.flutter.message"
  private var filePickerChannel = "com.acko.file_picker"
  private var contactChannel = "com.acko.contact"
  private var localChannel = "com.acko.flutter.message/local"
  var methodChannel: FlutterMethodChannel?
  var localMethodChannel: FlutterMethodChannel?
  var filePickerMethodChannel: FlutterMethodChannel?
  var contactMethodChannel: FlutterMethodChannel?
  private var enrollmaentCookies = ""
  private var ackoUserID = ""
  var filePicker: FilePickerInteractor?
  static let shared = FlutterHelper()
  var interaction: UIDocumentInteractionController?
  
    
  init() {
    
    //  #if DEBUG
    // baseUrl = "https://master.acko.com/" //TO DO:- Add a Dev Base url
    // #else
    // baseUrl = "https://master.acko.com/"
    // #endif
    
    //Set Up Flutter Engine
    flutterEngine = FlutterEngine(name: "ackoFlutterEngine", project: nil)
    flutterEngine.run()
    
    //Set up Flutter ViewController
    flutterViewController = FlutterViewController(engine: flutterEngine, nibName: nil, bundle: nil)
    flutterViewController.view.frame = CGRect(x: 0, y: 0, width: screenWidth, height: screenHeight)
    flutterViewController.view.autoresizingMask = [.flexibleWidth, .flexibleHeight]
    flutterViewController.view.backgroundColor = UIColor(red: 18, green: 18, blue: 18, alpha: 0)
    
    
    // setup plugins
    
    GeneratedPluginRegistrant.register(with: self.flutterEngine)
    NotificationCenter.default.addObserver(
      self,
      selector: #selector(self.invokeAckoLogout),
      name: NSNotification.Name(rawValue: "LogoutFromHealth"),
      object: nil)
    NotificationCenter.default.addObserver(self, selector: #selector(self.keyboardWillShow), name: UIResponder.keyboardWillShowNotification, object: nil)
    NotificationCenter.default.addObserver(self, selector: #selector(self.keyboardWillHide), name: UIResponder.keyboardWillHideNotification, object: nil)
  }
  
  deinit {
    NotificationCenter.default.removeObserver(self, name: Notification.Name("LogoutFromHealth"), object: nil)
    NotificationCenter.default.removeObserver(self, name: UIResponder.keyboardWillShowNotification, object: nil)
    NotificationCenter.default.removeObserver(self, name: UIResponder.keyboardWillHideNotification, object: nil)
  }
  
    func setPlatformChannel(){
        methodChannel = FlutterMethodChannel(name: channel, binaryMessenger: flutterViewController as! FlutterBinaryMessenger)
        setMethodChannel()
        localMethodChannel = FlutterMethodChannel(name: localChannel, binaryMessenger: flutterViewController as! FlutterBinaryMessenger)
        setLocalChannel()
        filePickerMethodChannel = FlutterMethodChannel(name: filePickerChannel, binaryMessenger: flutterViewController as! FlutterBinaryMessenger)
        setFilePickerChannel()
        contactMethodChannel = FlutterMethodChannel(name: contactChannel, binaryMessenger: flutterViewController as! FlutterBinaryMessenger)
        setContactChannel()
    }

  func setMethodChannel() {
      
    methodChannel?.setMethodCallHandler({ (methodCall, result) in
      
      switch methodCall.method {
          
      case FlutterMethodCall.getInitParams.rawValue :
        
        var params: [String: Any] = [
          "base_url" : Bundle.base_Url,
          "central_app_bff_base_url": Bundle.central_app_bff_base_url,
          "app_version" : self.versionNumber,
          "build_number" : self.buildNumber,
          "health_base_url": Bundle.health_base_url,
          "one_mg_api_base_url": Bundle.one_mg_api_base_url,
          "one_mg_meds_url": Bundle.one_mg_meds_url,
          "one_mg_meds_key": Bundle.one_mg_meds_key,
          "one_mg_labs_url": Bundle.one_mg_labs_url,
          "one_mg_labs_key": Bundle.one_mg_labs_key,
          "build_flavor": Bundle.buildFlavor,
          "acko_cer_decrypt_key": ACKO_CER_DECRYPT_KEY,
          "acko_cer_iv": ACKO_CER_IV
        ]
        
        params[AF_ID] = AppsFlyerLib.shared().getAppsFlyerUID()
        params[appsFlyer_dev_key] = AppsFlyerLib.shared().appsFlyerDevKey
        params[apple_app_id] = "id" + AppsFlyerLib.shared().appleAppID
        
        if let ad_id = getAdvertisingId(){
          params[AD_ID] = ad_id
        }
        
        if let trackingStatusValue = getTrackingStatus(){
            params[trackingStatus] = trackingStatusValue
        }
        
        if let isTrackingEnabledValue = getISTrackingEnabled(){
            params[isTrackingEnabled] = isTrackingEnabledValue
        }
        
        if let idfaValue = getIDFA(){
            params[idfa] = idfaValue
        }
          
        result(params as NSDictionary)
        break
        
      case FlutterMethodCall.call.rawValue:
        if let args = methodCall.arguments as? Dictionary<String, Any>, let phone = args["dialer_number"] as? String{
          print("phone  "+phone)
          self.openUrl(url:"tel://\(phone)")
        }
        break
      case FlutterMethodCall.mail.rawValue:
        if let mail = methodCall.arguments as? String{
          print("mail  "+mail)
          self.openUrl(url:"mailto:\(mail)")
        }
        
        break
        
      case FlutterMethodCall.share.rawValue:
        if let args = methodCall.arguments as? Dictionary<String, Any> , let url = args["path"] as? String {
            
            let fileURL = NSURL(fileURLWithPath: url)
            var filesToShare = [Any]()
            filesToShare.append(fileURL)
            let activityViewController = UIActivityViewController(activityItems: filesToShare, applicationActivities: nil)
            self.flutterViewController.present(activityViewController, animated: true, completion: nil)
        }
        break
        
      case FlutterMethodCall.shareIntent.rawValue:
        let args = methodCall.arguments as? Dictionary<String, Any>
        
        if let shareText = args?["share_text"] as? String {
          print("SHARE TEXT: "+shareText)
          let sharedObjects:[Any] = [shareText]
          let ac = UIActivityViewController(activityItems: sharedObjects, applicationActivities: nil)
          self.flutterViewController.present(ac, animated: true, completion:nil)
        }
        break
        
      case FlutterMethodCall.openBroswer.rawValue:
        if let url = methodCall.arguments as? String {
          self.openUrl(url: url)
        }
        break
        
      case FlutterMethodCall.openFile.rawValue:
        if let args = methodCall.arguments as? Dictionary<String, Any>, let url = args["path"] as? String{
          let fileUrl = URL(fileURLWithPath: url)
          let dummyVC = DummyViewController()
          self.interaction = UIDocumentInteractionController(url: fileUrl)
          self.interaction?.delegate = dummyVC
          self.interaction?.presentPreview(animated: true)
        }
        break
        
      case FlutterMethodCall.syncHealthData.rawValue:
        if let args = methodCall.arguments as? Dictionary<String, Any>, let cookies = args["cookie"] as? String, cookies != "null" {
          debugPrint("cookies received - ", cookies)
          self.enrollmaentCookies = cookies
          let cookieArray = self.enrollmaentCookies.split(separator: ";")
          debugPrint("cookieArray-", cookieArray)
          for cookie in cookieArray {
            let cookieFields = cookie.split(separator: "=")
            if let key = cookieFields.first {
              self.setCookie(key:String(key.trimmingCharacters(in: .whitespaces)), value: cookieFields.last as AnyObject)
            }
          }
          self.setCookie(key:"app_version", value: self.versionNumber as AnyObject)
          self.setCookie(key:"webview", value: "ios_app" as AnyObject)
          self.setCookie(key:"hide_header", value: "true" as AnyObject)
        }
        break
      case FlutterMethodCall.openAppStore.rawValue:
        self.openUrl(url:appStoreUrl)
        
      case FlutterMethodCall.logout.rawValue:
        self.deleteCookies()
        let webEngUser = WebEngage.sharedInstance()?.user
        webEngUser?.logout()
        DeeplinkManager.instance.userLoggedIn = false
        DeeplinkManager.instance.isHomePageLoaded = false
        break
        
      case FlutterMethodCall.myAccountLoadedEvent.rawValue:
        appDelegate.flutterCallBackListene(.myAccountLoadedEvent)
        self.methodChannel?.invokeMethod("sync_acko_auth_cookies", arguments: nil)
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
          DeeplinkManager.instance.userLoggedIn = true
          DeeplinkManager.instance.isHomePageLoaded = true
          DeeplinkManager.instance.cookiesToPass = self.enrollmaentCookies
          DeeplinkManager.instance.handleDeeplinkURL()
        }
        break
        
      case FlutterMethodCall.openSetting.rawValue:
        guard let settingsUrl = URL(string: UIApplication.openSettingsURLString) else {
          return
        }
        
        if UIApplication.shared.canOpenURL(settingsUrl) {
          UIApplication.shared.open(settingsUrl, completionHandler: { (success) in
          })
        }
        
        break
      case FlutterMethodCall.showInAppReview.rawValue:
        SKStoreReviewController.requestReview()
        break
        
      case FlutterMethodCall.login.rawValue:
        if let params = methodCall.arguments as? [String: Any], let prospectId = params["prospect_id"] as? String{
          let webEngUser = WebEngage.sharedInstance()?.user
          webEngUser?.login(prospectId)
          DeeplinkManager.instance.userLoggedIn = true
        }
      case FlutterMethodCall.webengage_login.rawValue:
        if let params = methodCall.arguments as? [String: Any], let prospectId = params["prospect_id"] as? String{
          let webEngUser = WebEngage.sharedInstance()?.user
          webEngUser?.logout()
          webEngUser?.login(prospectId)
        }
          
      case FlutterMethodCall.nativeWebView.rawValue:
        if let params = methodCall.arguments as? [String: Any], let url = params["loading_url"] as? String{
          let fileWebView = FileWebView()
          fileWebView.urlToLoad = url
          fileWebView.edgesForExtendedLayout = []
          if let appdelegate = UIApplication.shared.delegate as? AppDelegate, let navigationController = appdelegate.window!.rootViewController as? UINavigationController {
            navigationController.setNavigationBarHidden(false, animated: false)
            navigationController.navigationBar.backgroundColor = .white
            navigationController.navigationBar.isTranslucent = false
            navigationController.pushViewController(fileWebView, animated: false)
          }
        }
        
      case FlutterMethodCall.getLocationData.rawValue:
        let params: [String: Any] = self.getLocationData()
        result(params as NSDictionary)
        
      case FlutterMethodCall.appOpen.rawValue:
        appDelegate.flutterCallBackListene(.appOpen)
        
      case FlutterMethodCall.startPayment.rawValue:
          let paymentVC = PaymentsViewController()
          paymentVC.modalPresentationStyle = .overCurrentContext
          paymentVC.modalTransitionStyle = .crossDissolve // Optional for fade effect
          paymentVC.hyperServiceInstance = HyperServices()
          paymentVC.hyperServiceInstance?.hyperDelegate = paymentVC
          self.flutterViewController.present(paymentVC, animated: false)
          
//        self.flutterViewController.navigationController?.pushViewController(paymentVC, animated: true)
        
      case FlutterMethodCall.getFCMToken.rawValue:
          if let fcmtoken = fcmTokenValue {
               UNUserNotificationCenter.current().getNotificationSettings { settings in
                   let notificationEnabled = settings.authorizationStatus == .authorized
                   let params: [String: Any] = [
                       "fcm_token": fcmtoken,
                       "notification_enabled": notificationEnabled
                   ]
                   DispatchQueue.main.async {
                               result(params as NSDictionary)
                    }
               }
           } else {
               result(nil)
           }
      case FlutterMethodCall.setCalendarReminder.rawValue:
        if let params = methodCall.arguments as? [String: Any] {
          print(params)
          let title = params["title"] as? String
          let description = params["description"] as? String
          let startDate = params["start_date"] as? Int
          
          if startDate != nil{
            let s_date = Date(timeIntervalSince1970: Double(startDate!/1000))
            
            let event = CalendarEvent(title: title, startDate: s_date, desc: description)
            
            let calendar = CalendarHelper.shared
            calendar.eventStore.requestAccess(to: .event) { accessGranted, error in
              if accessGranted {
                calendar.addEventToCalendar(event: event) { (success, eventId) in
                  if success{
                    result(eventId)
                  }else {
                    debugPrint("ERROR ADDING EVENT")
                  }
                }
              }else{
                if let err = error{
                  debugPrint("CALENDAR ERROR: \(err.localizedDescription)")
                }
              }
            }
          }
        }

      case FlutterMethodCall.desiredAppIcon.rawValue:
          self.changeAppIcons()
          
      case FlutterMethodCall.changeOrientation.rawValue:
          if let params = methodCall.arguments as? [String: Any], let toPortrait = params["toPortrait"] as? Bool{
              self.changeOrientation(toPortrait: toPortrait)
          }
      case FlutterMethodCall.storeDeeplink.rawValue:
          if let args = methodCall.arguments as? Dictionary<String, Any>, let deeplinkValue = args["deeplink"] as? String{
              DeeplinkManager.instance.setAppDeeplink(deeplink: deeplinkValue)
              appDelegate.storedDeepLinkUntilNextAppOpen = StoredDeeplink(value: deeplinkValue, source: .FLUTTER)
              result(true)
          }
          result(false)
          
      case FlutterMethodCall.initMapsSDK.rawValue:
          if let args = methodCall.arguments as? Dictionary<String, Any>, let mapsApiKey = args["google_maps_api_key"] as? String{
              GMSServices.provideAPIKey(mapsApiKey)
          }
      case FlutterMethodCall.getAppSecrets.rawValue:
          var secretsMap: [String: String] = [:]
          
          if let infoDict = Bundle.main.infoDictionary {
              for key in AppSecretKey.allCases {
                  if let value = infoDict[key.rawValue] as? String {
                      secretsMap[key.rawValue] = value
                  }
              }
          }

          result(secretsMap)
          
      case FlutterMethodCall.getAppOpenDeepLink.rawValue :
          let params: [String: Any]? = appDelegate.getAppOpenDeeplink()
          result(params)
//          ["storedDeeplink" : DeeplinkManager.instance.getAppOpenDeepLink(),
//                                       "appsFlyerInitialized": appDelegate.isAppsflyerInitialized,
//                                       "appsflyerDeeplinkMediaSource": appDelegate.appsflyerMediaSource]
      case FlutterMethodCall.launchDeeplink.rawValue:
          result(appDelegate.launchDeeplink()?.toHashMap())
          
      case FlutterMethodCall.clearStoredDeeplink.rawValue:
              appDelegate.clearStoredDeeplink()
          result(true);
          

      case FlutterMethodCall.getDeviceId.rawValue :
          let identifierManager = PersistentIdentifierManager.shared
          let appIdentifier = identifierManager.persistentIdentifier
          UNUserNotificationCenter.current().getNotificationSettings { settings in
                 let notificationEnabled = settings.authorizationStatus == .authorized
                 let params: [String: Any] = [
                     "device_id": appIdentifier,
                     "notification_enabled": notificationEnabled
                 ]
                DispatchQueue.main.async {
                    result(params as NSDictionary)
                }
             }
      case FlutterMethodCall.getAdvertisementInfo.rawValue :
          let idfv = UIDevice.current.identifierForVendor?.uuidString ?? ""
          if #available(iOS 14, *) {
              let trackingAuthorizationStatus = ATTrackingManager.trackingAuthorizationStatus
              if trackingAuthorizationStatus == .authorized {
                  let idfa = ASIdentifierManager.shared().advertisingIdentifier.uuidString
                  let isLimitAdTrackingEnabled = !ASIdentifierManager.shared().isAdvertisingTrackingEnabled
                  let params: [String: Any] = [
                    "idfa": idfa,
                    "is_advertisement_disabled": isLimitAdTrackingEnabled,
                    "idfv": idfv
                ]
                  result(params as NSDictionary)
              } else {
                  let params: [String: Any] = [
                    "idfv": idfv
                ]
                  result(params as NSDictionary)
              }
          } else {
              let idfa = ASIdentifierManager.shared().advertisingIdentifier.uuidString
              let isLimitAdTrackingEnabled = !ASIdentifierManager.shared().isAdvertisingTrackingEnabled
              let params: [String: Any] = [
                "idfa": idfa,
                "is_advertisement_disabled": isLimitAdTrackingEnabled,
                "idfv": idfv
            ]
              result(params as NSDictionary)
          }
      case FlutterMethodCall.getUuid.rawValue:
          result(deviceId)
      default: break
      }
    })
  }
  
  func setLocalChannel() {
    
    localMethodChannel?.setMethodCallHandler({ (methodCall, result) in
      switch methodCall.method {
        
      case FlutterMethodCall.getAppsFlyerVisit.rawValue:
        result(nil)
        
      default: break
        
      }
    })
  }
    
    func changeOrientation(toPortrait : Bool) {
        if (toPortrait) {
            appDelegate.deviceOrientation = .portrait
        } else {
            appDelegate.deviceOrientation = .landscapeRight
        }
        
        if #available(iOS 16.0, *) {
            DispatchQueue.main.async {
                let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene
                self.flutterViewController.setNeedsUpdateOfSupportedInterfaceOrientations()
                self.flutterViewController.navigationController?.setNeedsUpdateOfSupportedInterfaceOrientations()
                windowScene?.requestGeometryUpdate(.iOS(interfaceOrientations: .landscape)) { error in
                    print(error)
                    print(windowScene?.effectiveGeometry ?? "")
                }
            }
        } else {
            let value = toPortrait ? UIInterfaceOrientation.portrait.rawValue : UIInterfaceOrientation.landscapeRight.rawValue
            UIDevice.current.setValue(value, forKey: "orientation")
        }
    }

    func changeAppIcons(){
        if (UIApplication.shared.alternateIconName == nil) {
            print("checkAndUpdateAppIcon : changing icon supported : \(UIApplication.shared.supportsAlternateIcons)")
            UIApplication.shared.setAlternateIconName("AppIcon-1") { error in
                if let error = error {
                    print("checkAndUpdateAppIcon : error occured in changing appIcon with \(error.localizedDescription)")
                }
                else {
                    print("checkAndUpdateAppIcon :appIcon changed")

                }
            }
        }
      }

  func getFilePicker() -> FilePickerInteractor {
    if let appdelegate = UIApplication.shared.delegate as? AppDelegate, let navigationController = appdelegate.window!.rootViewController as? UINavigationController, let topViewController = navigationController.topViewController {
      filePicker = FilePickerInteractor(viewController: topViewController)
      return filePicker ?? FilePickerInteractor(viewController: UIViewController())
    } else {
      print("Error getting file picker instance")
      return FilePickerInteractor(viewController: UIViewController())
    }
  }
    func setContactChannel(){
        contactMethodChannel?.setMethodCallHandler({ (methodCall, result) in
            if let args = methodCall.arguments as? Dictionary<String, Any> {
                switch methodCall.method {
                case FlutterMethodCall.saveContact.rawValue:
                    if let name = args["name"], let number = args["phone"]{
                        let contact = CNMutableContact()
                        contact.givenName = name as! String
                        contact.phoneNumbers.append(CNLabeledValue(label:"mobile",value:CNPhoneNumber(stringValue:number as! String)))
                        let store = CNContactStore()
                        do {
                            let saveRequest = CNSaveRequest()
                            saveRequest.add(contact, toContainerWithIdentifier: nil)
                            try store.execute(saveRequest)
                            result(true)
                        }   
                        catch {
                            result(false)
                        }
                        
                    }
                default:
                        result(FlutterMethodNotImplemented)
                }
            }
            
        })
    }

    func convertJsonStringToDict(inputJsonString: String?) -> [String: Any]? {
      if let payloadStr = inputJsonString, let data = payloadStr.data(using: .utf8){
        do {
          let payloadDict = try JSONSerialization.jsonObject(with: data, options: []) as? [String: Any]
          return payloadDict
        }catch {
          print(error.localizedDescription)
        }
      }
      return nil
    }

    func getFilteredValuesFromDict(forKeys keys: [String], fromDictionary dict: [String: Any]) -> [String] {
        // Filter the dictionary to include only the key-value pairs where the key is in the specified keys array
        let filteredPairs = dict.filter { keys.contains($0.key) }
        // Extract the values from the filtered key-value pairs
        let values = filteredPairs.map { ($0.value as? String) ?? ""}
        return values
    }

  func setFilePickerChannel() {

    filePickerMethodChannel?.setMethodCallHandler({ (methodCall, result) in
      if let args = methodCall.arguments as? Dictionary<String, Any> {

        switch methodCall.method {
        case FlutterMethodCall.pickFile.rawValue:
          if let fileExtension = args["filterExtensions"], let allowMultiple = args["allowMultiple"], let mimeType = args["mimeType"],let allowedMimeToUIData = args["mimetoui"], let allowExtensions = args["allowExtensions"] {

              var supportedDocTypes = [String]()

              if let typeOfMimeAllowed = allowedMimeToUIData as? String , let allowedExtension = allowExtensions as? [String] {
                  let dictOfAllowedMimes = (self.convertJsonStringToDict(inputJsonString: typeOfMimeAllowed ?? "") as? [String:Any]) ?? [:]
                  supportedDocTypes = self.getFilteredValuesFromDict(forKeys: allowedExtension, fromDictionary: dictOfAllowedMimes)
              }
              
              if let type = mimeType as? String {
                if !(supportedDocTypes.isEmpty) {
                      self.getFilePicker().openDocument(completion: { data in
                        result(data)
                      },mutipleAllowed: allowMultiple,allowExtensions: supportedDocTypes)
                } else if type == "image/*" {
                  self.getFilePicker().openGallery(completion: { data in
                    result(data)
                  },mutipleAllowed: allowMultiple)
                } else {
                    self.getFilePicker().openDocument(completion: { data in
                      result(data)
                    },mutipleAllowed: allowMultiple,allowExtensions: supportedDocTypes)
                }
              } else {
                  self.getFilePicker().openDocument(completion: { data in
                    result(data)
                  },mutipleAllowed: allowMultiple,allowExtensions: supportedDocTypes)
              }
          }
          
          
        case FlutterMethodCall.captureImageWithCompression.rawValue:
          let args = args["selfie_camera"]
          if let selfieMode = args as? Bool {
            if selfieMode {
              self.getFilePicker().openOnlyCamera(completion: {data in
                result(data)
              },isSelfieMode: true)
            } else{
              self.getFilePicker().openOnlyCamera(completion: {data in
                result(data)
              },isSelfieMode: false)
            }
          }else{
            self.getFilePicker().openOnlyCamera(completion: {data in
              result(data)
            },isSelfieMode: false)
          }
          
        case FlutterMethodCall.clearFiles.rawValue:
          print("clear files")
          
        default: break
          
        }
      } else if  methodCall.method == FlutterMethodCall.captureImageWithCompression.rawValue {
        self.getFilePicker().openOnlyCamera(completion:{ data in
          result(data)
        }, isSelfieMode: false)
      }
    })
  }
  
  func getLocationData() -> [String: Any] {
    var params: [String: Any] = [:]
    let locationManager: CLLocationManager = CLLocationManager()
    if CLLocationManager.locationServicesEnabled() {
      switch CLLocationManager.authorizationStatus() {
      case .authorizedAlways, .authorizedWhenInUse:
        let locationData = locationManager.location
        
        if let location = locationData{
          storeLocationData(location: location)
          params["longitude"] = location.coordinate.longitude
          params["latitude"] = location.coordinate.latitude
          params["accuracy"] = location.horizontalAccuracy
          params["permission"] = true
          
          return params
        }
      default:
        break
      }
    }
    
    if (UserDefaults.standard.value(forKey: "LAST_LAT") != nil) {
      params["longitude"] = UserDefaults.standard.value(forKey: "LAST_LAT")
      params["latitude"] = UserDefaults.standard.value(forKey: "LAST_LONG")
      params["accuracy"] = UserDefaults.standard.value(forKey: "LAST_ACC")
      params["permission"] = false
      
      return params
    }
    
    params["accuracy"] = -1
    params["permission"] = false
    return params
  }
  
  private func storeLocationData(location: CLLocation) {
    UserDefaults.standard.set(location.coordinate.latitude, forKey: "LAST_LAT")
    UserDefaults.standard.set(location.coordinate.longitude, forKey: "LAST_LONG")
    UserDefaults.standard.set(location.horizontalAccuracy, forKey: "LAST_ACC")
  }
  
  private func setCookie(key: String, value: AnyObject) {
    
    if key == "user_id" {
      if let value = value as? String { self.ackoUserID = value }
    }
    
    let cookieProps: [HTTPCookiePropertyKey : Any] = [
      HTTPCookiePropertyKey.domain: getDominNameFromURL(urlString: Bundle.web_base_url),
      HTTPCookiePropertyKey.path: "/",
      HTTPCookiePropertyKey.name: key,
      HTTPCookiePropertyKey.value: value,
      HTTPCookiePropertyKey.secure: "TRUE",
      HTTPCookiePropertyKey.expires: NSDate(timeIntervalSinceNow: TimeInterval(60 * 60 * 24 * 365))
    ]
    
    if let cookie = HTTPCookie(properties: cookieProps) {
      HTTPCookieStorage.shared.setCookie(cookie)
    }
  }
  
  private func getDominNameFromURL(urlString: String) -> String {
    if let url = URL(string: urlString)  {
      if let hostName = url.host  {
        
        let subStrings = hostName.components(separatedBy: ".")
        var domainName = ""
        let count = subStrings.count
        if count > 2 {
          domainName = subStrings[count - 2] + "." + subStrings[count - 1]
        } else if count == 2 {
          domainName = hostName
        }
        return "www.\(domainName)"
      }
    }
    return ""
  }
  
  private func deleteCookies() {
    let cookieStore = HTTPCookieStorage.shared
    for cookie in cookieStore.cookies ?? [] {
      cookieStore.deleteCookie(cookie)
    }
  }
  
  private func openUrl(url: String) {
    
    guard let finalUrl = URL(string: url),UIApplication.shared.canOpenURL(finalUrl) else {
      return
    }
    if #available(iOS 10.0, *) {
      UIApplication.shared.open(finalUrl, options: [:], completionHandler: nil)
    }
    
  }
  
  private func getHealthParams(args: [String: Any]) -> [String: Any] {
    let params = [
      "message": args["message"],
      "screen": args["screen"],
      "healthAuthToken": args["gmc_auth_token"],
      "healthETag": args["gmc_etag"],
      "customer_mobile": args["mobile_num"],
      "policyStage": args["gmc_policy_stage"],
      "gmcPolicyID": args["gmc_policy_id"],
      "enrollmaentCookies": self.enrollmaentCookies,
      "webBaseURL": Bundle.web_base_url,
      "ackoUserID": self.ackoUserID,
      "baseURL":Bundle.base_Url,
      "cashlessClaimUrl": Bundle.cashless_claim_url
    ]
    return params as [String : Any]
  }
  
  @objc func invokeAckoLogout() {
    self.methodChannel?.invokeMethod("gmc_user_logout", arguments: nil)
  }
  
  @objc func notifyKeyboardOpen(open: Bool) {
    self.methodChannel?.invokeMethod( FlutterMethodCall.keyboardOpenMethod.rawValue, arguments: open)
  }
  
  func handleDeeplink(_ link: String){
    //      let deeplinkData = DeeplinkHandler().getDeeplinkData(link) as [String: AnyObject]
    methodChannel?.invokeMethod(FlutterMethodCall.openPage.rawValue, arguments: link, result: { (_) in
    })
  }
  
  @objc
  private func keyboardWillShow(){
    self.notifyKeyboardOpen(open: true)
  }
  
  @objc
  private func keyboardWillHide(){
    self.notifyKeyboardOpen(open: false)
  }
}

class DummyViewController: NSObject, UIDocumentInteractionControllerDelegate {
  func documentInteractionControllerViewControllerForPreview(_ controller: UIDocumentInteractionController) -> UIViewController {
    return (UIApplication.shared.delegate?.window??.rootViewController)!
  }
  override init(){
  }
  required init?(coder: NSCoder) {
  }
}
