//
//  PersistentIdentifierManager.swift
//  Runner
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 12/09/24.
//

import Foundation

class PersistentIdentifierManager {
    
    static let shared = PersistentIdentifierManager()
    
    private let keychainService = "tech.acko.apple"
    private let keychainAccount = "uniqueDeviceId"
    private let queue = DispatchQueue(label: "com.acko.persistentIdLock")
    
    private var cachedUUID: String?

    var persistentIdentifier: String {
        queue.sync {
            if let cached = cachedUUID {
                return cached
            }

            if let uuidData = KeychainHelper.shared.read(keychainService, account: keychainAccount),
               let uuid = String(data: uuidData, encoding: .utf8) {
                cachedUUID = uuid
                return uuid
            } else {
                let newUUID = UUID().uuidString
                if let newUUIDData = newUUID.data(using: .utf8) {
                    KeychainHelper.shared.save(keychainService, account: keychainAccount, data: newUUIDData)
                    cachedUUID = newUUID
                }
                return newUUID
            }
        }
    }
}
