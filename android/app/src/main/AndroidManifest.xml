<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    tools:ignore="LockedOrientationActivity"
>
    <queries>
        <intent>
            <action android:name="android.support.customtabs.action.CustomTabsService" />
        </intent>
    </queries>

    <!-- Basic Internet Access-->
    <uses-permission android:name="android.permission.INTERNET" />

    <!-- Foreground Notification permission for  API > Android 13-->
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <!-- Ad id permission for API > Android 13-->
    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />

    <!--Android
    10+ uses scoped storage and does not require additional permission to read and write to storage
    hence, limiting permission to sdk 29-->
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="29" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="29" />

    <!-- Other network state access-->
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />

    <!-- Location Permissions-->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />

    <!-- Contacts, Calendar and other Utilities-->
    <uses-permission android:name="android.permission.READ_CONTACTS" />
    <uses-permission android:name="android.permission.WRITE_CONTACTS" />
    <uses-permission android:name="android.permission.WRITE_CALENDAR" />
    <uses-permission android:name="android.permission.VIBRATE" />

    <!-- Camera, Audio, Video capture-->
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.VIDEO_CAPTURE" />
    <uses-permission android:name="android.permission.AUDIO_CAPTURE" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />

    <!-- Sensors -->
    <uses-feature android:name="android.hardware.sensor.gyroscope" android:required="false" />

    <!-- Other-->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
    <uses-permission android:name="android.permission.ACTIVITY_RECOGNITION" />

    <uses-feature android:name="android.hardware.camera" android:required="false" />
    <uses-permission android:name="com.android.launcher.permission.INSTALL_SHORTCUT"/>
    <uses-feature android:name="android.hardware.camera.front" android:required="false" />
    <uses-feature
        android:name="android.hardware.camera.autofocus"
        android:required="false" />
    <queries>
        <intent>
            <action android:name="android.media.action.IMAGE_CAPTURE" />
        </intent>
    </queries>
    <queries>
        <intent>
            <action android:name="android.media.action.VIDEO_CAPTURE" />
        </intent>
    </queries>

    <!-- Chrome tab -->
    <queries>
        <intent>
            <action android:name="android.support.customtabs.action.CustomTabsService" />
        </intent>
    </queries>

    <application
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:allowBackup="false"
        android:fullBackupContent="false"
        android:launchMode="singleTask"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:name=".AckoApplication"
        android:theme="@style/AppTheme"
        android:usesCleartextTraffic="false"
        tools:replace="android:allowBackup, android:fullBackupContent,android:usesCleartextTraffic"
    >

        <activity
            android:name=".AckoActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:theme="@style/LaunchTheme"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:windowSoftInputMode="adjustResize"
            tools:ignore="Instantiatable">
            <meta-data android:name="flutter_deeplinking_enabled" android:value="false" />

            <intent-filter
                android:autoVerify="true"
                tools:targetApi="m">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data
                    android:host="www.acko.com"
                    android:scheme="https" />
            </intent-filter>
            <intent-filter
                android:autoVerify="true"
                tools:targetApi="m">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="ackoapp.onelink.me"
                    android:scheme="https" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="mainactivity"
                    android:scheme="ackoapp" />
            </intent-filter>

            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <activity-alias
            android:name=".DefaultAppIconActivity"
            android:targetActivity=".AckoActivity"
            android:enabled="false"
            android:icon="@mipmap/ic_launcher2"
            android:exported="true">

            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity-alias>

        <receiver android:name=".DownloadCompletedBroadcastReceiver"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.DOWNLOAD_COMPLETE"/>
            </intent-filter>
        </receiver>

        <activity
            android:name="com.google.android.gms.tagmanager.PreviewActivity"
            android:exported="true"
            android:noHistory="true">  <!-- optional, removes the previewActivity from the activity
            stack. -->
            <intent-filter>
                <data android:scheme="tagmanager.c.com.acko.android" />
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
            </intent-filter>
        </activity>
        <activity
            android:name=".feature.notification.NotificationClickHandlerActivity"
            android:excludeFromRecents="true"
            android:launchMode="singleTask"
            android:exported="false"
            android:taskAffinity="" />
        <provider
            android:name="com.pichillilorenzo.flutter_inappwebview_android.InAppWebViewFileProvider"
            android:authorities="${applicationId}.flutter_inappwebview_android.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/provider_paths" />
        </provider>
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.com.acko.fileProvider"
            android:exported="false"
            android:grantUriPermissions="true"
            tools:replace="android:authorities">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/provider_path" />
        </provider>

        <activity
            android:name=".payment.PaymentActivity"
            android:screenOrientation="portrait"
            android:exported="false"
            android:theme="@style/Theme.Transparent" />

        <receiver
            android:name=".feature.smsretrievalapi.SmsRetrievalReceiver"
            android:exported="false"
            android:permission="com.google.android.gms.auth.api.phone.permission.SEND">
            <intent-filter>
                <action android:name="com.google.android.gms.auth.api.phone.SMS_RETRIEVED" />
            </intent-filter>
        </receiver>
        <activity android:name="in.juspay.hypersdk.core.CustomtabResult"
            tools:replace="exported"
            android:exported="false" />
        <meta-data
            android:name="firebase_performance_logcat_enabled"
            android:value="false" />
        <meta-data
            android:name="firebase_performance_collection_enabled"
            android:value="true" />
        <meta-data
            android:name="com.webengage.sdk.android.environment"
            android:value="in" />
        <meta-data
            android:name="com.google.android.gms.ads.AD_MANAGER_APP"
            android:value="true" />
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_icon"
            android:resource="@drawable/ic_notification" />

        <meta-data
            android:name="com.google.firebase.messaging.default_notification_color"
            android:resource="@color/purple" />

        <meta-data
            android:name="com.google.firebase.messaging.default_notification_channel_id"
            android:value="@string/channel_name" />
        <meta-data
            android:name="com.facebook.sdk.ApplicationId"
            android:value="@string/facebook_app_id" />

        <service
            android:name=".feature.notification.AckoDroidMessagingService"
            android:exported="false">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service> <!--
        Google campaign management components -->
        <receiver
            android:exported="false"
            android:name="com.google.android.gms.analytics.AnalyticsReceiver"
            android:enabled="true"
            android:permission="android.permission.WAKE_LOCK"
            tools:replace="android:exported">
            <intent-filter>
                <action android:name="com.google.android.gms.analytics.ANALYTICS_DISPATCH" />
            </intent-filter>
        </receiver>

        <receiver
            android:name="com.appsflyer.SingleInstallBroadcastReceiver"
            android:exported="false">
            <intent-filter>
                <action android:name="com.android.vending.INSTALL_REFERRER" />
            </intent-filter>
        </receiver>

        <service
            android:name="com.google.android.gms.analytics.AnalyticsService"
            android:enabled="true"
            android:exported="false"
            tools:replace="android:exported" />

        <meta-data
            android:name="com.google.android.gms.version"
            android:value="@integer/google_play_services_version" />
        <meta-data
            android:name="io.fabric.ApiKey"
            android:value="004551663b019b749e9821f94589f1da2b7a3aee" />
        <meta-data
            android:name="com.facebook.sdk.ApplicationId"
            android:value="@string/facebook_app_id" />

        <meta-data android:name="com.google.android.geo.API_KEY"
            android:value="${GOOGLE_MAPS_API_KEY}" />

        <!-- Don't delete the meta-data below. This is used by the Flutter tool to generate
        GeneratedPluginRegistrant.java -->
        <meta-data
            android:name="flutterEmbedding"
            android:value="2" />

        <meta-data
            android:name="io.flutter.embedding.android.EnableImpeller"
            android:value="false" />
    </application>
</manifest>
